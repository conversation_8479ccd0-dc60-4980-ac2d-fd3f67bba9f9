<!-- ÖREB Data Display Template -->
<div class="oereb-data-container">
    <!-- API Query Link -->
    {% if api_query_url %}
    <div class="api-query-info">
        <p class="api-query-label">API Query:</p>
        <a href="{{ api_query_url }}" target="_blank" class="api-query-link">{{ api_query_url }}</a>
    </div>
    {% endif %}

    <!-- Direct Cantonal ÖREB Links -->
    {% if cantonal_urls and property_info.egrid %}
    <div class="cantonal-oereb-links">
        <p class="cantonal-links-label">Kantonaler ÖREB-Service ({{ canton }}):</p>
        <div class="cantonal-links-container cantonal-links-inline">
            {% if cantonal_urls.xml %}
            <a href="{{ cantonal_urls.xml }}"
               target="_blank" class="cantonal-link cantonal-link-xml" title="XML-Auszug vom kantonalen ÖREB-Service">
                📄 XML
            </a>
            {% endif %}
            {% if cantonal_urls.pdf %}
            <a href="{{ cantonal_urls.pdf }}"
               target="_blank" class="cantonal-link cantonal-link-pdf" title="PDF-Auszug vom kantonalen ÖREB-Service">
                📋 PDF
            </a>
            {% endif %}
            {% if cantonal_urls.json %}
            <a href="{{ cantonal_urls.json }}"
               target="_blank" class="cantonal-link cantonal-link-json" title="JSON-Auszug vom kantonalen ÖREB-Service">
                🔗 JSON
            </a>
            {% endif %}
            {% if cantonal_urls.url %}
            <a href="{{ cantonal_urls.url }}"
               target="_blank" class="cantonal-link cantonal-link-url" title="URL-Auszug vom kantonalen ÖREB-Service">
                🌐 URL
            </a>
            {% endif %}
        </div>
    </div>
    {% endif %}



    <!-- Debug Information -->
    {% if oereb_data.error %}
    <div class="oereb-section oereb-error-debug">
        <h4>⚠️ XML-Parsing-Fehler</h4>
        <p><strong>Fehler:</strong> {{ oereb_data.error }}</p>
        {% if oereb_data.raw_xml_preview %}
        <details>
            <summary>XML-Vorschau anzeigen</summary>
            <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 12px; overflow-x: auto; white-space: pre-wrap;">{{ oereb_data.raw_xml_preview }}</pre>
        </details>
        {% endif %}
    </div>
    {% endif %}



    <!-- Structured ÖREB Data Display -->
    {% if oereb_data.structured_data %}
    <div class="oereb-section structured-data-display">
        <h4>📊 Strukturierte ÖREB-Daten</h4>

        <!-- Extract Information -->
        {% if oereb_data.structured_data.extract_info %}
        <div class="extract-info">
            <h5>Auszug-Informationen</h5>
            <div class="info-grid">
                {% if oereb_data.structured_data.extract_info.creation_date %}
                <div class="info-item">
                    <span class="label">Erstellungsdatum:</span>
                    <span class="value">{{ oereb_data.structured_data.extract_info.creation_date }}</span>
                </div>
                {% endif %}
                {% if oereb_data.structured_data.extract_info.extract_identifier %}
                <div class="info-item">
                    <span class="label">Auszug-ID:</span>
                    <span class="value">{{ oereb_data.structured_data.extract_info.extract_identifier }}</span>
                </div>
                {% endif %}
                {% if oereb_data.structured_data.extract_info.update_date %}
                <div class="info-item">
                    <span class="label">Aktualisierungsdatum:</span>
                    <span class="value">{{ oereb_data.structured_data.extract_info.update_date }}</span>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Real Estate Information -->
        {% if oereb_data.structured_data.real_estate %}
        <div class="real-estate-info">
            <h5>Grundstück-Informationen</h5>
            <div class="info-grid">
                {% for key, value in oereb_data.structured_data.real_estate.items() %}
                {% if value %}
                <div class="info-item">
                    <span class="label">{{ key|replace('_', ' ')|title }}:</span>
                    <span class="value">{{ value }}</span>
                </div>
                {% endif %}
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Logos and Images -->
    {% if oereb_data.logos and (oereb_data.logos.plr_cadastre_logo or oereb_data.logos.federal_logo or oereb_data.logos.cantonal_logo or oereb_data.logos.municipality_logo or oereb_data.logos.qr_code or oereb_data.logos.symbols or oereb_data.logos.maps) %}
    <div class="oereb-section logos-images-display">
        <h4>🖼️ Logos und Bilder</h4>

        <!-- Main Logos -->
        <div class="main-logos">
            {% if oereb_data.logos.plr_cadastre_logo %}
            <div class="logo-item">
                <h5>ÖREB-Kataster Logo</h5>
                <img src="data:image/{{ oereb_data.logos.plr_cadastre_logo.format }};base64,{{ oereb_data.logos.plr_cadastre_logo.data }}"
                     alt="ÖREB-Kataster Logo" class="oereb-logo" style="max-width: 200px; max-height: 100px;">
            </div>
            {% endif %}

            {% if oereb_data.logos.federal_logo %}
            <div class="logo-item">
                <h5>Bundeslogo</h5>
                <img src="data:image/{{ oereb_data.logos.federal_logo.format }};base64,{{ oereb_data.logos.federal_logo.data }}"
                     alt="Bundeslogo" class="oereb-logo" style="max-width: 200px; max-height: 100px;">
            </div>
            {% endif %}

            {% if oereb_data.logos.cantonal_logo %}
            <div class="logo-item">
                <h5>Kantonslogo</h5>
                <img src="data:image/{{ oereb_data.logos.cantonal_logo.format }};base64,{{ oereb_data.logos.cantonal_logo.data }}"
                     alt="Kantonslogo" class="oereb-logo" style="max-width: 200px; max-height: 100px;">
            </div>
            {% endif %}

            {% if oereb_data.logos.municipality_logo %}
            <div class="logo-item">
                <h5>Gemeindelogo</h5>
                <img src="data:image/{{ oereb_data.logos.municipality_logo.format }};base64,{{ oereb_data.logos.municipality_logo.data }}"
                     alt="Gemeindelogo" class="oereb-logo" style="max-width: 200px; max-height: 100px;">
            </div>
            {% endif %}
        </div>

        <!-- QR Code -->
        {% if oereb_data.logos.qr_code %}
        <div class="qr-code-section">
            <h5>QR-Code</h5>
            <img src="data:image/{{ oereb_data.logos.qr_code.format }};base64,{{ oereb_data.logos.qr_code.data }}"
                 alt="QR-Code" class="qr-code" style="max-width: 150px; max-height: 150px; border: 1px solid #ddd;">
        </div>
        {% endif %}

        <!-- Maps -->
        {% if oereb_data.logos.maps %}
        <div class="maps-section">
            <details>
                <summary><h5 style="display: inline;">Karten ({{ oereb_data.logos.maps|length }})</h5></summary>
                <div class="maps-container" style="margin-top: 10px;">
                    {% for map_image in oereb_data.logos.maps %}
                    <div class="map-item" style="margin-bottom: 15px; border: 1px solid #ddd; border-radius: 4px; padding: 10px;">
                        <h6>Karte ({{ map_image.language }})</h6>
                        <img src="data:image/{{ map_image.format }};base64,{{ map_image.data }}"
                             alt="ÖREB Karte" class="map-image"
                             style="max-width: 100%; height: auto; border: 1px solid #ccc;">
                    </div>
                    {% endfor %}
                </div>
            </details>
        </div>
        {% endif %}

        <!-- Symbols -->
        {% if oereb_data.logos.symbols %}
        <div class="symbols-section">
            <details>
                <summary><h5 style="display: inline;">Symbole ({{ oereb_data.logos.symbols|length }})</h5></summary>
                <div class="symbols-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)); gap: 10px; margin-top: 10px;">
                    {% for symbol in oereb_data.logos.symbols %}
                    <div class="symbol-item" style="text-align: center; padding: 10px; border: 1px solid #eee; border-radius: 4px;">
                        <img src="data:image/{{ symbol.format }};base64,{{ symbol.data }}"
                             alt="{{ symbol.context }}" class="symbol-image"
                             style="max-width: 50px; max-height: 50px; display: block; margin: 0 auto 5px;">
                        <small style="font-size: 11px; color: #666;">{{ symbol.context }}</small>
                    </div>
                    {% endfor %}
                </div>
            </details>
        </div>
        {% endif %}
    </div>
    {% endif %}





    <!-- Restrictions -->
    {% if oereb_data.restrictions %}
    <div class="oereb-section">
        <h4>Eigentumsbeschränkungen ({{ oereb_data.restrictions|length }})</h4>
        <div class="restrictions-list">
            {% for restriction in oereb_data.restrictions %}
            <div class="restriction-item">
                {% if restriction.topic %}
                    <div class="restriction-topic">{{ restriction.topic }}</div>
                {% endif %}

                <div class="restriction-details">
                    {% if restriction.legend_text %}
                        <div class="restriction-detail">
                            <span class="label">Beschreibung:</span>
                            <span class="value">{{ restriction.legend_text }}</span>
                        </div>
                    {% endif %}
                    {% if restriction.type_code %}
                        <div class="restriction-detail">
                            <span class="label">Code:</span>
                            <span class="value">{{ restriction.type_code }}</span>
                        </div>
                    {% endif %}
                    {% if restriction.lawstatus %}
                        <div class="restriction-detail">
                            <span class="label">Rechtsstatus:</span>
                            <span class="value">{{ restriction.lawstatus }}</span>
                        </div>
                    {% endif %}
                    {% if restriction.area %}
                        <div class="restriction-detail">
                            <span class="label">Betroffene Fläche:</span>
                            <span class="value">{{ restriction.area }} m²</span>
                        </div>
                    {% endif %}
                    {% if restriction.length %}
                        <div class="restriction-detail">
                            <span class="label">Betroffene Länge:</span>
                            <span class="value">{{ restriction.length }} m</span>
                        </div>
                    {% endif %}
                    {% if restriction.part_in_percent %}
                        <div class="restriction-detail">
                            <span class="label">Anteil:</span>
                            <span class="value">{{ restriction.part_in_percent }}%</span>
                        </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Legal Provisions -->
    {% if oereb_data.legal_provisions %}
    <div class="oereb-section">
        <h4>Rechtsvorschriften ({{ oereb_data.legal_provisions|length }})</h4>
        <div class="legal-provisions-list">
            {% for provision in oereb_data.legal_provisions %}
            <div class="legal-provision-item">
                {% if provision.title %}
                    <div class="provision-title">{{ provision.title }}</div>
                {% endif %}

                <div class="provision-details">
                    {% if provision.abbreviation %}
                        <div class="provision-detail">
                            <span class="label">Abkürzung:</span>
                            <span class="value">{{ provision.abbreviation }}</span>
                        </div>
                    {% endif %}
                    {% if provision.number %}
                        <div class="provision-detail">
                            <span class="label">Nummer:</span>
                            <span class="value">{{ provision.number }}</span>
                        </div>
                    {% endif %}
                    {% if provision.text_at_web %}
                        <div class="provision-detail">
                            <span class="label">Link:</span>
                            <span class="value">
                                <a href="{{ provision.text_at_web }}" target="_blank" rel="noopener">
                                    Volltext anzeigen ↗
                                </a>
                            </span>
                        </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Documents -->
    {% if oereb_data.documents %}
    <div class="oereb-section">
        <h4>Dokumente ({{ oereb_data.documents|length }})</h4>
        <div class="documents-list">
            {% for document in oereb_data.documents %}
            <div class="document-item">
                {% if document.title %}
                    <div class="document-title">{{ document.title }}</div>
                {% endif %}

                <div class="document-details">
                    {% if document.type %}
                        <div class="document-detail">
                            <span class="label">Typ:</span>
                            <span class="value">{{ document.type }}</span>
                        </div>
                    {% endif %}
                    {% if document.official_number %}
                        <div class="document-detail">
                            <span class="label">Nummer:</span>
                            <span class="value">{{ document.official_number }}</span>
                        </div>
                    {% endif %}
                    {% if document.web_reference %}
                        <div class="document-detail">
                            <span class="label">Link:</span>
                            <span class="value">
                                <a href="{{ document.web_reference }}" target="_blank" rel="noopener">
                                    Dokument öffnen ↗
                                </a>
                            </span>
                        </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Extract all legal links once and store them globally with real titles from XML -->
    {% set all_rechtsvorschriften = [] %}
    {% set all_gesetzliche_grundlagen = [] %}
    {% set processed_urls = [] %}

    <!-- Extract from all XML elements - Find URLs and their associated titles -->
    {% if oereb_data.all_elements %}
        {% for url_element in oereb_data.all_elements %}
            {% set url_index = loop.index0 %}
            {% set element_text = url_element.text|string if url_element.text else '' %}
            {% if element_text and element_text not in processed_urls %}
                {% if element_text.startswith('http') %}
                    <!-- Skip WMS/map URLs as they are not legal documents -->
                    {% if 'wms' in element_text.lower() or 'getmap' in element_text.lower() %}
                        {% set element_text = '' %}
                    {% else %}
                        <!-- Find the actual title from nearby elements -->
                        {% set link_title = 'Dokument' %}
                        {% set best_title = '' %}
                        {% set best_distance = 999 %}

                        <!-- Look for title in elements within 5 positions before and after the URL -->
                        {% for title_element in oereb_data.all_elements %}
                            {% set title_index = loop.index0 %}
                            {% set distance = title_index - url_index %}
                            {% if distance >= -5 and distance <= 5 and distance != 0 %}
                                {% set title_text = title_element.text|string if title_element.text else '' %}

                                <!-- Check if this looks like a real title -->
                                {% if title_text and title_text|length > 3 and not title_text.startswith('http') %}
                                    <!-- Skip generic status texts -->
                                    {% if title_text not in ['Rechtskräftig', 'Rechtsvorschrift', 'Gemeinde (Gde.)', '000.1', 'inKraft'] %}
                                        <!-- Prefer titles at distance -4, but take any good title if none at -4 -->
                                        {% if distance == -4 %}
                                            {% set best_title = title_text %}
                                            {% set best_distance = distance|abs %}
                                        {% elif best_title == '' and distance >= -5 and distance <= -1 %}
                                            {% set best_title = title_text %}
                                            {% set best_distance = distance|abs %}
                                        {% endif %}
                                    {% endif %}
                                {% endif %}
                            {% endif %}
                        {% endfor %}

                        <!-- Use the best title found, or fallback to 'Dokument' -->
                        {% if best_title %}
                            {% set link_title = best_title %}
                        {% endif %}

                        <!-- Determine category based on URL patterns -->
                        {% set is_rechtsvorschrift = false %}
                        {% if 'oereblex' in element_text %}
                            {% set is_rechtsvorschrift = true %}
                        {% elif 'stadtzug.ch' in element_text or 'gemeinde' in element_text.lower() %}
                            {% set is_rechtsvorschrift = true %}
                        {% elif '.pdf' in element_text.lower() %}
                            {% set is_rechtsvorschrift = true %}
                        {% elif 'gesetz' in element_text.lower() or 'verordnung' in element_text.lower() %}
                            {% set is_rechtsvorschrift = true %}
                        {% endif %}

                        <!-- Add to appropriate category if URL is valid -->
                        {% if element_text %}
                            {% if is_rechtsvorschrift %}
                                {% set _ = all_rechtsvorschriften.append({
                                    'url': element_text,
                                    'title': link_title
                                }) %}
                            {% else %}
                                {% set _ = all_gesetzliche_grundlagen.append({
                                    'url': element_text,
                                    'title': link_title
                                }) %}
                            {% endif %}
                            {% set _ = processed_urls.append(element_text) %}
                        {% endif %}
                    {% endif %}
                {% endif %}
            {% endif %}
        {% endfor %}
    {% endif %}

    <!-- Concerned Themes with integrated legal links -->
    {% if oereb_data.concerned_themes %}
    <div class="oereb-section">
        <h4>Betroffene Themen ({{ oereb_data.concerned_themes|length }})</h4>
        <div class="themes-list">
            {% for theme in oereb_data.concerned_themes %}
            <div class="theme-item concerned">
                <div class="theme-header">
                    <div class="theme-code">{{ theme.code }}</div>
                    <div class="theme-text">{{ theme.text }}</div>
                </div>

                <!-- Show legal links only under the first theme to avoid duplication -->
                {% if loop.first and (all_rechtsvorschriften or all_gesetzliche_grundlagen) %}
                <div class="theme-legal-links">
                    {% if all_rechtsvorschriften %}
                    <div class="legal-subsection">
                        <h6>Rechtsvorschriften</h6>
                        <div class="legal-links-list">
                            {% for link in all_rechtsvorschriften %}
                            <div class="legal-link-item">
                                <a href="{{ link.url }}" target="_blank" rel="noopener" title="{{ link.url }}">
                                    {{ link.title }}
                                </a>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    {% if all_gesetzliche_grundlagen %}
                    <div class="legal-subsection">
                        <h6>Gesetzliche Grundlagen</h6>
                        <div class="legal-links-list">
                            {% for link in all_gesetzliche_grundlagen %}
                            <div class="legal-link-item">
                                <a href="{{ link.url }}" target="_blank" rel="noopener" title="{{ link.url }}">
                                    {{ link.title }}
                                </a>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Not Concerned Themes -->
    {% if oereb_data.not_concerned_themes %}
    <div class="oereb-section">
        <h4>Nicht betroffene Themen ({{ oereb_data.not_concerned_themes|length }})</h4>
        <div class="themes-list">
            {% for theme in oereb_data.not_concerned_themes %}
            <div class="theme-item not-concerned">
                <div class="theme-code">{{ theme.code }}</div>
                <div class="theme-text">{{ theme.text }}</div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Themes Without Data -->
    {% if oereb_data.themes_without_data %}
    <div class="oereb-section">
        <h4>Themen ohne Daten ({{ oereb_data.themes_without_data|length }})</h4>
        <div class="themes-list">
            {% for theme in oereb_data.themes_without_data %}
            <div class="theme-item no-data">
                <div class="theme-code">{{ theme.code }}</div>
                <div class="theme-text">{{ theme.text }}</div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- General Information -->
    {% if oereb_data.general_information %}
    <div class="oereb-section">
        <h4>Allgemeine Informationen ({{ oereb_data.general_information|length }})</h4>
        <div class="general-info-list">
            {% for info in oereb_data.general_information %}
            <div class="general-info-item">
                {% if info.title %}
                    <div class="info-title">{{ info.title }}</div>
                {% endif %}
                {% if info.content %}
                    <div class="info-content">{{ info.content }}</div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Base Data -->
    {% if oereb_data.base_data %}
    <div class="oereb-section">
        <h4>Grundlagendaten ({{ oereb_data.base_data|length }})</h4>
        <div class="base-data-list">
            {% for data in oereb_data.base_data %}
            <div class="base-data-item">
                {% if data.title %}
                    <div class="data-title">{{ data.title }}</div>
                {% endif %}
                {% if data.provider %}
                    <div class="data-provider">Anbieter: {{ data.provider }}</div>
                {% endif %}
                {% if data.model_link %}
                    <div class="data-link">
                        <a href="{{ data.model_link }}" target="_blank" rel="noopener">
                            Modell anzeigen ↗
                        </a>
                    </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Glossary -->
    {% if oereb_data.glossary %}
    <div class="oereb-section">
        <h4>Glossar ({{ oereb_data.glossary|length }})</h4>
        <div class="glossary-list">
            {% for entry in oereb_data.glossary %}
            <div class="glossary-item">
                {% if entry.title %}
                    <div class="glossary-title">{{ entry.title }}</div>
                {% endif %}
                {% if entry.content %}
                    <div class="glossary-content">{{ entry.content }}</div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}



    <!-- Responsible Office -->
    {% if oereb_data.responsible_office and oereb_data.responsible_office.name %}
    <div class="oereb-section">
        <h4>Zuständige Stelle</h4>
        <div class="office-info">
            <div class="office-item">
                <span class="label">Name:</span>
                <span class="value">{{ oereb_data.responsible_office.name }}</span>
            </div>
            {% if oereb_data.responsible_office.uid %}
            <div class="office-item">
                <span class="label">UID:</span>
                <span class="value">{{ oereb_data.responsible_office.uid }}</span>
            </div>
            {% endif %}
            {% if oereb_data.responsible_office.street and oereb_data.responsible_office.number %}
            <div class="office-item">
                <span class="label">Adresse:</span>
                <span class="value">{{ oereb_data.responsible_office.street }} {{ oereb_data.responsible_office.number }}</span>
            </div>
            {% endif %}
            {% if oereb_data.responsible_office.postal_code and oereb_data.responsible_office.city %}
            <div class="office-item">
                <span class="label">Ort:</span>
                <span class="value">{{ oereb_data.responsible_office.postal_code }} {{ oereb_data.responsible_office.city }}</span>
            </div>
            {% endif %}
            {% if oereb_data.responsible_office.office_at_web %}
            <div class="office-item">
                <span class="label">Website:</span>
                <span class="value">
                    <a href="{{ oereb_data.responsible_office.office_at_web }}" target="_blank" rel="noopener">
                        {{ oereb_data.responsible_office.office_at_web }} ↗
                    </a>
                </span>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Certification Information -->
    {% if oereb_data.certification or oereb_data.certification_at_web %}
    <div class="oereb-section">
        <h4>Zertifizierung</h4>
        <div class="certification-info">
            {% if oereb_data.certification %}
            <div class="certification-item">
                <span class="label">Zertifikat:</span>
                <span class="value">{{ oereb_data.certification }}</span>
            </div>
            {% endif %}
            {% if oereb_data.certification_at_web %}
            <div class="certification-item">
                <span class="label">Zertifikat-Link:</span>
                <span class="value">
                    <a href="{{ oereb_data.certification_at_web }}" target="_blank" rel="noopener">
                        Zertifikat anzeigen ↗
                    </a>
                </span>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- No Data Message -->
    {% if not oereb_data.restrictions and not oereb_data.legal_provisions and not oereb_data.documents %}
    <div class="oereb-section">
        <div class="no-data-message">
            <p>Keine spezifischen ÖREB-Beschränkungen für dieses Grundstück gefunden.</p>
            <p class="note">Dies bedeutet nicht, dass keine Beschränkungen existieren. Konsultieren Sie die offiziellen Quellen für vollständige Informationen.</p>
        </div>
    </div>
    {% endif %}

    <!-- All Elements Display (Fallback/Debug) - Moved to bottom -->
    {% if oereb_data.all_elements %}
    <div class="oereb-section all-elements-display">
        <details>
            <summary><h4 style="display: inline;">📋 Alle XML-Daten ({{ oereb_data.all_elements|length }} Elemente)</h4></summary>
            <div style="max-height: 600px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9; margin-top: 10px;">
                {% for element in oereb_data.all_elements %}
                <div style="margin-bottom: 8px; padding: 6px; border-left: 3px solid #007bff; background: white; border-radius: 3px;">
                    <strong style="color: #007bff; font-family: monospace; white-space: nowrap;">{{ element.tag.split('}')[-1] if '}' in element.tag else element.tag }}:</strong>
                    <span style="margin-left: 8px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{ element.text }}</span>
                    {% if element.attrib %}
                    <small style="color: #666; display: block; margin-top: 4px; font-family: monospace; white-space: nowrap;">Attribute: {{ element.attrib }}</small>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </details>
    </div>
    {% endif %}

    <!-- Footer with coordinates -->
    {% if coordinates %}
    <div class="oereb-footer">
        <small class="coordinates-info">
            Koordinaten: {{ "%.1f"|format(coordinates.x) }}, {{ "%.1f"|format(coordinates.y) }} (EPSG:2056)
        </small>
    </div>
    {% endif %}
</div>
